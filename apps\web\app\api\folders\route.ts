import { NextRequest, NextResponse } from "next/server";
import { getFolderNames } from "../../utils/getFolderNames";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const route = searchParams.get("route") || "";
    
    const folders = await getFolderNames(route);
    
    return NextResponse.json({ folders });
  } catch (error) {
    console.error("Error in folders API:", error);
    return NextResponse.json(
      { error: "Failed to fetch folders" },
      { status: 500 }
    );
  }
}
