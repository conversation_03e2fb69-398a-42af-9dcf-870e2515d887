import Link from "next/link";
import { getFolderNames } from "./utils/getFolderNames";

export default async function Page() {
  const folderNames = await getFolderNames();

  return (
    <div className="flex items-center justify-center min-h-svh">
      <div className="flex flex-col items-center justify-center gap-4">
        <div className="flex flex-col gap-2">
          {folderNames.map((folderName) => (
            <Link
              key={folderName}
              href={`/${folderName}`}
              className="text-foreground"
            >
              {folderName.charAt(0).toUpperCase() + folderName.slice(1)}
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
