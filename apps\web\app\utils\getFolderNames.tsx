import fs from "node:fs/promises";
import path from "path";

export async function getFolderNames(currentRoute: string = "") {
  try {
    // Build the directory path based on current route
    const routeParts = currentRoute.split("/").filter(Boolean);
    const targetDir = path.join(process.cwd(), "app", ...routeParts);

    // Check if the directory exists
    try {
      await fs.access(targetDir);
    } catch {
      // If directory doesn't exist, fall back to app root
      const appDir = path.join(process.cwd(), "app");
      const entries = await fs.readdir(appDir, { withFileTypes: true });

      const folders = entries
        .filter((entry) => entry.isDirectory())
        .map((entry) => entry.name)
        .filter(
          (name) =>
            !name.startsWith(".") &&
            name !== "node_modules" &&
            name !== "utils",
        );

      return folders;
    }

    const entries = await fs.readdir(targetDir, { withFileTypes: true });

    // Filter only directories and exclude common files
    const folders = entries
      .filter((entry) => entry.isDirectory())
      .map((entry) => entry.name)
      .filter(
        (name) =>
          !name.startsWith(".") && name !== "node_modules" && name !== "utils",
      );

    return folders;
  } catch (error) {
    console.error("Error reading directory:", error);
    return [];
  }
}
