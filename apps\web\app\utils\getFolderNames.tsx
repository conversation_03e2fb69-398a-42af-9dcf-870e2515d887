import fs from "node:fs/promises";
import path from "path";

export async function getFolderNames() {
  try {
    const appDir = path.join(process.cwd(), "app");
    const entries = await fs.readdir(appDir, { withFileTypes: true });

    // Filter only directories and exclude common files
    const folders = entries
      .filter((entry) => entry.isDirectory())
      .map((entry) => entry.name)
      .filter((name) => !name.startsWith(".") && name !== "node_modules");

    return folders;
  } catch (error) {
    console.error("Error reading directory:", error);
    return [];
  }
}
