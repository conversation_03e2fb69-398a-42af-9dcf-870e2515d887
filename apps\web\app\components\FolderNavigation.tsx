"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

interface FolderNavigationProps {
  initialFolders?: string[];
}

export function FolderNavigation({ initialFolders = [] }: FolderNavigationProps) {
  const pathname = usePathname();
  const [folders, setFolders] = useState<string[]>(initialFolders);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchFolders = async () => {
      setLoading(true);
      try {
        const response = await fetch(`/api/folders?route=${encodeURIComponent(pathname)}`);
        if (response.ok) {
          const data = await response.json();
          setFolders(data.folders);
        }
      } catch (error) {
        console.error("Error fetching folders:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchFolders();
  }, [pathname]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-svh">
        <div className="text-foreground">Loading...</div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center min-h-svh">
      <div className="flex flex-col items-center justify-center gap-4">
        <div className="flex flex-col gap-2">
          {folders.length === 0 ? (
            <div className="text-foreground">No folders found in current route</div>
          ) : (
            folders.map((folderName) => (
              <Link
                key={folderName}
                href={`${pathname === "/" ? "" : pathname}/${folderName}`}
                className="text-foreground hover:underline"
              >
                {folderName.charAt(0).toUpperCase() + folderName.slice(1)}
              </Link>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
